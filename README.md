# HTTP配置日志服务

一个简单的HTTP服务，提供配置读取和日志输出功能。

## 功能特性

- 读取INI格式配置文件 (app.conf)
- 读取JSON格式配置文件 (config.json)
- 提供HTTP接口获取配置信息
- 支持通过HTTP接口写入日志
- 支持获取日志文件内容
- 自动日志轮转（按天）
- 健康检查接口

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

### 方式1: 使用Flask版本 (需要安装依赖)
```bash
pip install -r requirements.txt
python app.py
```
服务将在 http://localhost:5000 启动

### 方式2: 使用简化版本 (无需额外依赖)
```bash
python simple_server.py
```
服务将在 http://localhost:8000 启动

推荐使用简化版本，因为它只使用Python标准库，无需安装额外依赖。

## API接口

### 1. 健康检查
- **URL**: `/health`
- **方法**: GET
- **描述**: 检查服务状态

```bash
# Flask版本 (端口5000)
curl http://localhost:5000/health

# 简化版本 (端口8000)
curl http://localhost:8000/health
```

### 2. 获取配置
- **URL**: `/config`
- **方法**: GET
- **描述**: 获取所有配置信息

```bash
# Flask版本
curl http://localhost:5000/config

# 简化版本
curl http://localhost:8000/config
```

### 3. 写入日志
- **URL**: `/log`
- **方法**: POST
- **描述**: 写入日志消息

```bash
# Flask版本
curl -X POST http://localhost:5000/log \
  -H "Content-Type: application/json" \
  -d '{"level": "info", "message": "这是一条测试日志"}'

# 简化版本
curl -X POST http://localhost:8000/log \
  -H "Content-Type: application/json" \
  -d '{"level": "info", "message": "这是一条测试日志"}'
```

支持的日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL

### 4. 获取日志
- **URL**: `/logs`
- **方法**: GET
- **参数**:
  - `lines`: 获取最近N行日志（默认100行）
- **描述**: 获取日志文件内容

```bash
# Flask版本
curl "http://localhost:5000/logs?lines=50"

# 简化版本
curl "http://localhost:8000/logs?lines=50"
```

## 配置文件

### app.conf (INI格式)
```ini
[server]
host = 0.0.0.0
port = 5000
debug = true

[database]
host = localhost
port = 3306
username = root
password = 123456
database = test_db
```

### config.json (JSON格式)
```json
{
  "app_name": "HTTP配置日志服务",
  "version": "1.0.0",
  "environment": "development"
}
```

## 日志文件

日志文件保存在 `log/app.log`，支持按天自动轮转，保留最近10天的日志文件。

## 示例响应

### 成功响应
```json
{
  "status": "success",
  "timestamp": "2025-08-04T10:30:00.123456",
  "data": {...}
}
```

### 错误响应
```json
{
  "status": "error",
  "timestamp": "2025-08-04T10:30:00.123456",
  "message": "错误描述"
}
```

## 测试服务

提供了自动化测试脚本来验证所有接口功能：

```bash
# 启动服务 (在一个终端)
python simple_server.py

# 运行测试 (在另一个终端)
python test_server.py
```

测试脚本会自动测试所有API接口，包括：
- 健康检查
- 配置读取
- 日志写入（各种级别）
- 日志获取

## 文件结构

```
.
├── app.py              # Flask版本HTTP服务
├── simple_server.py    # 简化版HTTP服务（推荐）
├── test_server.py      # 测试脚本
├── app.conf           # INI格式配置文件
├── config.json        # JSON格式配置文件
├── requirements.txt   # Python依赖
├── README.md          # 说明文档
└── log/               # 日志目录
    └── app.log        # 日志文件
```
