# -*- coding: utf-8 -*-

import os
import json
import configparser
from datetime import datetime
from flask import Flask, jsonify, request
import logging
import logging.handlers

app = Flask(__name__)

# 配置日志
def setup_logger():
    """设置日志配置"""
    # 确保log目录存在
    if not os.path.exists('log'):
        os.makedirs('log')
    
    logger = logging.getLogger('http_service')
    logger.setLevel(logging.DEBUG)
    
    # 文件处理器 - 按天轮转
    file_handler = logging.handlers.TimedRotatingFileHandler(
        'log/app.log', 
        when='midnight', 
        interval=1, 
        backupCount=10,
        encoding='utf-8'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    
    # 格式化器
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] [%(filename)s:%(lineno)d] ::%(message)s'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 初始化日志
logger = setup_logger()

def read_config():
    """读取配置文件"""
    config = {}
    
    # 读取INI格式配置文件
    if os.path.exists('app.conf'):
        try:
            parser = configparser.ConfigParser()
            parser.read('app.conf', encoding='utf-8')
            
            for section in parser.sections():
                config[section] = dict(parser.items(section))
            
            logger.info(f"成功读取配置文件 app.conf")
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
    
    # 读取JSON格式配置文件
    if os.path.exists('config.json'):
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                json_config = json.load(f)
                config.update(json_config)
            
            logger.info(f"成功读取配置文件 config.json")
        except Exception as e:
            logger.error(f"读取JSON配置文件失败: {e}")
    
    return config

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    logger.info("健康检查请求")
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'service': 'HTTP配置日志服务'
    })

@app.route('/config', methods=['GET'])
def get_config():
    """获取配置信息接口"""
    try:
        logger.info("收到获取配置请求")
        config = read_config()
        
        response = {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'config': config
        }
        
        logger.info(f"成功返回配置信息，配置项数量: {len(config)}")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'message': str(e)
        }), 500

@app.route('/log', methods=['POST'])
def write_log():
    """写入日志接口"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求体不能为空'
            }), 400
        
        level = data.get('level', 'info').upper()
        message = data.get('message', '')
        
        if not message:
            return jsonify({
                'status': 'error',
                'message': '日志消息不能为空'
            }), 400
        
        # 根据级别写入日志
        if level == 'DEBUG':
            logger.debug(message)
        elif level == 'INFO':
            logger.info(message)
        elif level == 'WARNING':
            logger.warning(message)
        elif level == 'ERROR':
            logger.error(message)
        elif level == 'CRITICAL':
            logger.critical(message)
        else:
            logger.info(message)
        
        return jsonify({
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'message': '日志写入成功'
        })
        
    except Exception as e:
        logger.error(f"写入日志失败: {e}")
        return jsonify({
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'message': str(e)
        }), 500

@app.route('/logs', methods=['GET'])
def get_logs():
    """获取日志文件内容接口"""
    try:
        lines = request.args.get('lines', 100, type=int)
        log_file = 'log/app.log'
        
        if not os.path.exists(log_file):
            return jsonify({
                'status': 'error',
                'message': '日志文件不存在'
            }), 404
        
        # 读取最后N行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        logger.info(f"返回最近 {len(recent_lines)} 行日志")
        
        return jsonify({
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'logs': [line.strip() for line in recent_lines],
            'total_lines': len(recent_lines)
        })
        
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        return jsonify({
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'message': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("HTTP服务启动")
    app.run(host='0.0.0.0', port=5000, debug=True)
