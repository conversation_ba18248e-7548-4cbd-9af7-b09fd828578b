# -*- coding: utf-8 -*-

import logging
import logging.handlers
import os
import platform


def is_platform_windows():
    return 'Windows' in platform.system()

def is_platform_linux():
    return 'Linux' in platform.system()

# change dir
app_path = os.getcwd()
if is_platform_linux():
    app_path = os.path.dirname(app_path)
    os.chdir(app_path)

print(app_path)

# log conf
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)
streamhandler = logging.StreamHandler()
filehandler = logging.handlers.TimedRotatingFileHandler('log/app.log', when='midnight', interval=1, backupCount=10)
formatter = logging.Formatter('%(asctime)s [%(levelname)s] [%(filename)s:%(lineno)d] ::%(message)s')
streamhandler.setFormatter(formatter)
filehandler.setFormatter(formatter)
logger.addHandler(filehandler)
logger.addHandler(streamhandler)
